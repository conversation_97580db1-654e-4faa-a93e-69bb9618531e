#!/usr/bin/env python3
"""
Test Redis connection with the provided credentials
"""
import redis
import sys
from urllib.parse import urlparse

def test_redis_connection():
    """Test Redis connection"""
    # Try direct connection parameters first
    host = "************"
    port = 6379
    password = "NseTdSpPQkmPb+J6xp&KM*HM"  # Unencoded password
    
    try:
        # Parse the Redis URL
        parsed = urlparse(redis_url)
        
        print(f"Connecting to Redis at {parsed.hostname}:{parsed.port}")
        print(f"Using password: {'*' * len(parsed.password) if parsed.password else 'None'}")
        
        # Create Redis client
        r = redis.from_url(redis_url, decode_responses=True)
        
        # Test connection
        print("Testing connection...")
        r.ping()
        print("✅ Redis connection successful!")
        
        # Test basic operations
        print("\nTesting basic operations...")
        r.set("test_key", "test_value")
        value = r.get("test_key")
        print(f"Set and get test: {value}")
        
        # Clean up
        r.delete("test_key")
        print("✅ Basic operations successful!")
        
        # Get server info
        info = r.info()
        print(f"\nRedis server version: {info.get('redis_version', 'Unknown')}")
        print(f"Used memory: {info.get('used_memory_human', 'Unknown')}")
        print(f"Connected clients: {info.get('connected_clients', 'Unknown')}")
        
        return True
        
    except redis.ConnectionError as e:
        print(f"❌ Redis connection failed: {e}")
        return False
    except redis.AuthenticationError as e:
        print(f"❌ Redis authentication failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Redis test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_redis_connection()
    sys.exit(0 if success else 1)
