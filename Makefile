# WhatsApp CRM Agent - Production Makefile

# Configuration
PYTHON := python3
VENV := venv
WORKERS := $(shell nproc --all 2>/dev/null || echo 4)
HOST := 0.0.0.0
PORT := 8001
LOG_LEVEL := info

# Colors
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m

.PHONY: help install dev prod test clean logs status stop health setup

# Default target
help: ## Show this help message
	@echo -e "$(BLUE)WhatsApp CRM Agent - Production Commands$(NC)"
	@echo -e "$(BLUE)=======================================$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Setup and Installation
setup: ## Initial setup - create venv and install dependencies
	@echo -e "$(YELLOW)📦 Setting up WhatsApp CRM Agent...$(NC)"
	$(PYTHON) -m venv $(VENV)
	$(VENV)/bin/pip install --upgrade pip
	$(VENV)/bin/pip install -r requirements.txt
	$(VENV)/bin/pip install gunicorn uvicorn[standard]
	mkdir -p logs
	@echo -e "$(GREEN)✅ Setup complete!$(NC)"

install: setup ## Alias for setup

# Development
dev: ## Start development server with auto-reload
	@echo -e "$(BLUE)🚀 Starting development server...$(NC)"
	$(VENV)/bin/python -m uvicorn src.main:app --reload --host $(HOST) --port $(PORT) --log-level $(LOG_LEVEL)

# Production
prod: ## Start production server with Gunicorn
	@echo -e "$(BLUE)🚀 Starting production server...$(NC)"
	@echo -e "$(GREEN)Workers: $(WORKERS)$(NC)"
	@echo -e "$(GREEN)Host: $(HOST):$(PORT)$(NC)"
	@echo -e "$(GREEN)Logs: logs/access.log, logs/error.log$(NC)"
	mkdir -p logs
	PYTHONPATH=src $(VENV)/bin/gunicorn src.main:app \
		--worker-class uvicorn.workers.UvicornWorker \
		--workers $(WORKERS) \
		--bind $(HOST):$(PORT) \
		--max-requests 1000 \
		--max-requests-jitter 100 \
		--preload \
		--keepalive 2 \
		--timeout 120 \
		--graceful-timeout 30 \
		--access-logfile logs/access.log \
		--error-logfile logs/error.log \
		--log-level $(LOG_LEVEL) \
		--capture-output \
		--enable-stdio-inheritance

# Background production
prod-bg: ## Start production server in background
	@echo -e "$(BLUE)🚀 Starting production server in background...$(NC)"
	mkdir -p logs
	nohup $(MAKE) prod > logs/startup.log 2>&1 & echo $$! > logs/server.pid
	@echo -e "$(GREEN)✅ Server started in background (PID: $$(cat logs/server.pid))$(NC)"

# Testing
test: ## Run tests
	@echo -e "$(YELLOW)🧪 Running tests...$(NC)"
	$(VENV)/bin/python -m pytest tests/ -v

test-imports: ## Test that all imports work correctly
	@echo -e "$(YELLOW)🔍 Testing imports...$(NC)"
	mkdir -p logs
	$(VENV)/bin/python -c "from src.main import app; print('✅ Application imports successfully')"
	$(VENV)/bin/python -c "from src.agents import conversation_agent, crm_agent, analytics_agent, ticketing_agent, knowledge_agent; print('✅ All agents imported successfully')"

# Monitoring and Logs
logs: ## Show application logs
	@echo -e "$(BLUE)📝 Application Logs:$(NC)"
	@if [ -f logs/error.log ]; then tail -f logs/error.log; else echo "No error logs found"; fi

logs-access: ## Show access logs
	@echo -e "$(BLUE)📝 Access Logs:$(NC)"
	@if [ -f logs/access.log ]; then tail -f logs/access.log; else echo "No access logs found"; fi

logs-all: ## Show all logs
	@echo -e "$(BLUE)📝 All Logs:$(NC)"
	@if [ -f logs/error.log ] && [ -f logs/access.log ]; then \
		tail -f logs/error.log logs/access.log; \
	else \
		echo "No logs found"; \
	fi

status: ## Check server status
	@echo -e "$(BLUE)📊 Server Status:$(NC)"
	@if [ -f logs/server.pid ]; then \
		PID=$$(cat logs/server.pid); \
		if ps -p $$PID > /dev/null 2>&1; then \
			echo -e "$(GREEN)✅ Server running (PID: $$PID)$(NC)"; \
		else \
			echo -e "$(RED)❌ Server not running$(NC)"; \
		fi; \
	else \
		echo -e "$(YELLOW)⚠️  No PID file found$(NC)"; \
	fi

health: ## Check application health
	@echo -e "$(BLUE)🏥 Health Check:$(NC)"
	@curl -s http://$(HOST):$(PORT)/health || echo -e "$(RED)❌ Health check failed$(NC)"

stop: ## Stop background server
	@echo -e "$(YELLOW)🛑 Stopping server...$(NC)"
	@if [ -f logs/server.pid ]; then \
		PID=$$(cat logs/server.pid); \
		if ps -p $$PID > /dev/null 2>&1; then \
			kill $$PID && echo -e "$(GREEN)✅ Server stopped$(NC)"; \
			rm -f logs/server.pid; \
		else \
			echo -e "$(YELLOW)⚠️  Server not running$(NC)"; \
			rm -f logs/server.pid; \
		fi; \
	else \
		echo -e "$(YELLOW)⚠️  No PID file found$(NC)"; \
	fi

# Maintenance
clean: ## Clean up logs and temporary files
	@echo -e "$(YELLOW)🧹 Cleaning up...$(NC)"
	rm -rf logs/*.log
	rm -rf logs/server.pid
	rm -rf __pycache__
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo -e "$(GREEN)✅ Cleanup complete$(NC)"

clean-all: clean ## Clean everything including venv
	@echo -e "$(YELLOW)🧹 Deep cleaning...$(NC)"
	rm -rf $(VENV)
	@echo -e "$(GREEN)✅ Deep cleanup complete$(NC)"

# Process Management
ps: ## Show running processes
	@echo -e "$(BLUE)📊 WhatsApp CRM Agent Processes:$(NC)"
	@ps aux | grep -E "(gunicorn|uvicorn)" | grep -v grep || echo "No processes found"

kill-all: ## Kill all related processes
	@echo -e "$(YELLOW)🛑 Killing all WhatsApp CRM Agent processes...$(NC)"
	@pkill -f "gunicorn.*src.main:app" || true
	@pkill -f "uvicorn.*src.main:app" || true
	@rm -f logs/server.pid
	@echo -e "$(GREEN)✅ All processes killed$(NC)"

# Monitoring
monitor: ## Monitor server resources
	@echo -e "$(BLUE)📊 Server Monitoring:$(NC)"
	@echo "Press Ctrl+C to stop monitoring"
	@while true; do \
		clear; \
		echo -e "$(BLUE)=== WhatsApp CRM Agent Monitor ===$(NC)"; \
		echo -e "$(GREEN)Time: $$(date)$(NC)"; \
		echo ""; \
		if [ -f logs/server.pid ]; then \
			PID=$$(cat logs/server.pid); \
			if ps -p $$PID > /dev/null 2>&1; then \
				echo -e "$(GREEN)✅ Server Status: Running (PID: $$PID)$(NC)"; \
				echo ""; \
				ps -p $$PID -o pid,ppid,pcpu,pmem,etime,cmd --no-headers; \
			else \
				echo -e "$(RED)❌ Server Status: Not Running$(NC)"; \
			fi; \
		else \
			echo -e "$(YELLOW)⚠️  Server Status: Unknown (no PID file)$(NC)"; \
		fi; \
		echo ""; \
		echo -e "$(BLUE)Recent Error Logs:$(NC)"; \
		if [ -f logs/error.log ]; then \
			tail -5 logs/error.log; \
		else \
			echo "No error logs"; \
		fi; \
		sleep 5; \
	done

# Systemd Service Management (requires sudo)
install-service: ## Install systemd service
	@echo -e "$(YELLOW)📦 Installing systemd service...$(NC)"
	@if [ "$$EUID" -eq 0 ]; then \
		cp whatsapp-crm.service /etc/systemd/system/; \
		systemctl daemon-reload; \
		systemctl enable whatsapp-crm; \
		echo -e "$(GREEN)✅ Service installed and enabled$(NC)"; \
	else \
		echo -e "$(RED)❌ Please run with sudo: sudo make install-service$(NC)"; \
	fi

service-start: ## Start systemd service
	@echo -e "$(BLUE)🚀 Starting systemd service...$(NC)"
	sudo systemctl start whatsapp-crm
	@echo -e "$(GREEN)✅ Service started$(NC)"

service-stop: ## Stop systemd service
	@echo -e "$(YELLOW)🛑 Stopping systemd service...$(NC)"
	sudo systemctl stop whatsapp-crm
	@echo -e "$(GREEN)✅ Service stopped$(NC)"

service-restart: ## Restart systemd service
	@echo -e "$(BLUE)🔄 Restarting systemd service...$(NC)"
	sudo systemctl restart whatsapp-crm
	@echo -e "$(GREEN)✅ Service restarted$(NC)"

service-status: ## Check systemd service status
	@echo -e "$(BLUE)📊 Service Status:$(NC)"
	sudo systemctl status whatsapp-crm --no-pager

service-logs: ## Show systemd service logs
	@echo -e "$(BLUE)📝 Service Logs:$(NC)"
	sudo journalctl -u whatsapp-crm -f

# Quick commands
start: prod-bg ## Quick start (alias for prod-bg)
restart: stop start ## Restart server
reload: stop prod-bg ## Reload server (alias for restart)
force-restart: kill-all start ## Force restart (kill all processes and start fresh)
