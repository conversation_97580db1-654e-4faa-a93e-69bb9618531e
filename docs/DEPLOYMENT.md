# WhatsApp CRM Agent - Production Deployment Guide

This guide covers production deployment using the included Makefile and systemd service.

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Clone and setup
git clone <your-repo-url>
cd whatsapp-agent

# Initial setup (creates venv, installs dependencies)
make setup
```

### 2. Configure Environment
```bash
# Copy and edit environment file
cp .env.example .env
nano .env
```

### 3. Start Production Server
```bash
# Start in background
make start

# Or start in foreground
make prod
```

## 📋 Available Commands

### Setup & Installation
- `make setup` - Initial setup (venv + dependencies)
- `make install` - Alias for setup

### Development
- `make dev` - Start development server with auto-reload
- `make test` - Run tests
- `make test-imports` - Test that all imports work

### Production
- `make prod` - Start production server (foreground)
- `make prod-bg` - Start production server (background)
- `make start` - Quick start (alias for prod-bg)

### Monitoring & Control
- `make status` - Check server status
- `make health` - Health check via HTTP
- `make logs` - Show application logs
- `make logs-access` - Show access logs
- `make logs-all` - Show all logs
- `make monitor` - Real-time monitoring dashboard

### Process Management
- `make stop` - Stop background server
- `make restart` - Restart server
- `make reload` - Reload server
- `make force-restart` - Kill all processes and restart
- `make ps` - Show running processes
- `make kill-all` - Kill all related processes

### Systemd Service (Production)
- `sudo make install-service` - Install systemd service
- `make service-start` - Start systemd service
- `make service-stop` - Stop systemd service
- `make service-restart` - Restart systemd service
- `make service-status` - Check service status
- `make service-logs` - Show service logs

### Maintenance
- `make clean` - Clean logs and temp files
- `make clean-all` - Clean everything including venv

## 🔧 Production Configuration

### Environment Variables
Set these in your `.env` file:
```bash
# Server
HOST=0.0.0.0
PORT=8001
WORKERS=4  # Will auto-calculate if not set

# Database
DATABASE_URL=postgresql://user:pass@host:port/db
REDIS_URL=redis://host:port/db

# API Keys
OPENAI_API_KEY=your_key
AGNO_API_KEY=your_key

# Ticketing
FRESHDESK_API_KEY=your_key
FRESHDESK_DOMAIN=your_domain
JIRA_API_TOKEN=your_token
JIRA_SERVER_URL=your_url
JIRA_EMAIL=your_email
```

### Systemd Service (Recommended for Production)

1. **Install the service:**
   ```bash
   sudo make install-service
   ```

2. **Start the service:**
   ```bash
   make service-start
   ```

3. **Check status:**
   ```bash
   make service-status
   ```

4. **View logs:**
   ```bash
   make service-logs
   ```

### Manual Production Setup

If you prefer manual setup:

```bash
# Install dependencies
make setup

# Start with custom configuration
WORKERS=8 HOST=127.0.0.1 PORT=8080 make prod
```

## 📊 Monitoring

### Real-time Monitoring
```bash
make monitor
```

### Log Monitoring
```bash
# Application logs
make logs

# Access logs
make logs-access

# All logs
make logs-all
```

### Health Checks
```bash
# HTTP health check
make health

# Process status
make status

# System processes
make ps
```

## 🔒 Security Considerations

1. **Firewall**: Only expose port 8000 if needed
2. **SSL/TLS**: Use reverse proxy (nginx/apache) for HTTPS
3. **Environment**: Keep `.env` file secure
4. **User**: Run service as non-root user (www-data)
5. **Logs**: Rotate logs regularly

## 🔄 Deployment Workflow

### Initial Deployment
```bash
# 1. Setup
make setup

# 2. Configure
cp .env.example .env
# Edit .env with your settings

# 3. Test
make test-imports

# 4. Install service (production)
sudo make install-service

# 5. Start
make service-start
```

### Updates
```bash
# 1. Stop service
make service-stop

# 2. Pull updates
git pull

# 3. Update dependencies
make setup

# 4. Restart
make service-restart
```

### Rollback
```bash
# 1. Stop service
make service-stop

# 2. Checkout previous version
git checkout <previous-commit>

# 3. Restart
make service-restart
```

## 🐛 Troubleshooting

### Service Won't Start
```bash
# Check status
make service-status

# Check logs
make service-logs

# Check processes
make ps

# Force restart
make force-restart
```

### High Memory Usage
```bash
# Reduce workers
WORKERS=2 make restart
```

### Port Already in Use
```bash
# Kill all processes
make kill-all

# Start fresh
make start
```

### Permission Issues
```bash
# Fix log permissions
sudo chown -R $USER:$USER logs/

# Fix service permissions
sudo chown -R www-data:www-data /opt/whatsapp-agent/
```
