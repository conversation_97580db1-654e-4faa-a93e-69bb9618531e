# WhatsApp CRM Agent - Solo Developer Plan

## Quick Start (EC2 Deployment)

### Tech Stack
- **Backend**: Python + FastAPI + Agno AI
- **Database**: PostgreSQL + Redis
- **WhatsApp**: Selenium WebDriver (MVP) → Business API (later)
- **AI**: OpenAI GPT-4/3.5
- **Deployment**: EC2 → ECS (migration path)

### MVP Timeline (4 Weeks)

#### Week 1: Foundation
- [ ] EC2 setup (Ubuntu 22.04, Docker, PostgreSQL, Redis)
- [ ] Project structure + Git repo
- [ ] WhatsApp Web automation (Selenium)
- [ ] Basic FastAPI server

#### Week 2: Core Agents
- [ ] Conversation Agent (basic responses)
- [ ] WhatsApp tools (send/receive messages)
- [ ] Database models (conversations, messages)
- [ ] OpenAI integration

#### Week 3: Ticketing
- [ ] Ticketing Agent
- [ ] Freshdesk integration (start with one system)
- [ ] Knowledge Agent (simple FAQ)
- [ ] Ticket creation workflow

#### Week 4: Polish & Deploy
- [ ] CRM Agent (basic client tracking)
- [ ] Error handling + logging
- [ ] EC2 production deployment
- [ ] Basic monitoring

## Project Structure (Simplified)

```
whatsapp-agent/
├── requirements.txt
├── docker-compose.yml
├── .env.example
├── README.md
│
├── src/
│   ├── main.py                 # FastAPI + Agno crew
│   ├── config.py               # Settings
│   │
│   ├── agents/
│   │   ├── conversation.py     # Main WhatsApp handler
│   │   ├── ticketing.py        # Ticket management
│   │   ├── knowledge.py        # FAQ/solutions
│   │   └── crm.py             # Client tracking
│   │
│   ├── tools/
│   │   ├── whatsapp.py         # WhatsApp operations
│   │   ├── ticketing.py        # Freshdesk/Jira APIs
│   │   └── database.py         # DB operations
│   │
│   ├── models/
│   │   ├── client.py
│   │   ├── conversation.py
│   │   ├── message.py
│   │   └── ticket.py
│   │
│   └── integrations/
│       ├── whatsapp_web.py     # Selenium automation
│       ├── freshdesk.py        # Freshdesk API
│       └── openai_client.py    # OpenAI wrapper
│
├── scripts/
│   ├── setup_ec2.sh           # EC2 setup script
│   ├── deploy.sh              # Deployment script
│   └── backup.sh              # Database backup
│
└── deployment/
    ├── docker-compose.prod.yml
    ├── nginx.conf
    └── systemd/
        └── whatsapp-agent.service
```

## EC2 Setup

### Instance Requirements
- **Type**: t3.medium (2 vCPU, 4GB RAM)
- **Storage**: 20GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Security Groups**: SSH (22), HTTP (80), HTTPS (443), Custom (8000)

### Quick Setup Script
```bash
#!/bin/bash
# setup_ec2.sh

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Chrome for Selenium
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update && sudo apt install -y google-chrome-stable

# Clone and setup project
git clone <your-repo> whatsapp-agent
cd whatsapp-agent
cp .env.example .env
# Edit .env with your keys

# Start services
docker-compose up -d
```

## Core Agent Implementation

### Conversation Agent
```python
from agno import Agent, tool

@tool
def send_whatsapp_message(group_id: str, message: str):
    """Send message to WhatsApp group"""
    # Selenium implementation
    pass

conversation_agent = Agent(
    name="WhatsApp Handler",
    role="Customer service agent",
    goal="Handle WhatsApp messages and provide helpful responses",
    tools=[send_whatsapp_message, search_knowledge, create_ticket],
    memory=True
)
```

### Ticketing Agent
```python
ticketing_agent = Agent(
    name="Ticket Manager",
    role="Support ticket specialist",
    goal="Create and manage support tickets efficiently",
    tools=[create_freshdesk_ticket, update_ticket, search_similar_tickets],
    memory=True
)
```

## Database Schema (Essential Tables)

```sql
-- Clients
CREATE TABLE clients (
    id UUID PRIMARY KEY,
    name VARCHAR(255),
    phone VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Groups
CREATE TABLE groups (
    id UUID PRIMARY KEY,
    client_id UUID REFERENCES clients(id),
    whatsapp_group_id VARCHAR(255),
    name VARCHAR(255),
    is_active BOOLEAN DEFAULT true
);

-- Conversations
CREATE TABLE conversations (
    id UUID PRIMARY KEY,
    group_id UUID REFERENCES groups(id),
    started_at TIMESTAMP DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active'
);

-- Messages
CREATE TABLE messages (
    id UUID PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id),
    sender_id VARCHAR(255),
    content TEXT,
    message_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tickets
CREATE TABLE tickets (
    id UUID PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id),
    external_id VARCHAR(255),
    system VARCHAR(50),
    title VARCHAR(255),
    status VARCHAR(50),
    priority VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Deployment Strategy

### Phase 1: EC2 Single Instance
- Docker Compose with all services
- PostgreSQL + Redis containers
- Nginx reverse proxy
- SSL with Let's Encrypt
- Basic monitoring (logs)

### Phase 2: ECS Migration (Later)
- Convert to ECS task definitions
- RDS for PostgreSQL
- ElastiCache for Redis
- Application Load Balancer
- CloudWatch monitoring

## Environment Variables

```bash
# .env
DATABASE_URL=postgresql://user:pass@localhost:5432/whatsapp_crm
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_key
AGNO_API_KEY=your_agno_key
FRESHDESK_API_KEY=your_freshdesk_key
FRESHDESK_DOMAIN=your_domain.freshdesk.com
WHATSAPP_HEADLESS=true
SECRET_KEY=your_secret_key
```

## Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/whatsapp_crm
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./whatsapp_session:/app/whatsapp_session

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: whatsapp_crm
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## Monitoring (Basic)

```bash
# Simple health check endpoint
@app.get("/health")
def health_check():
    return {"status": "healthy", "timestamp": datetime.now()}

# Log to file
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/whatsapp-agent.log'),
        logging.StreamHandler()
    ]
)
```

## Development Workflow

1. **Local Development**
   ```bash
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   docker-compose up -d db redis
   python src/main.py
   ```

2. **Testing**
   ```bash
   pytest tests/ -v
   ```

3. **Deployment**
   ```bash
   ./scripts/deploy.sh
   ```

## Cost Estimation (Monthly)

- **EC2 t3.medium**: ~$30
- **Storage (20GB)**: ~$2
- **OpenAI API**: ~$50-100 (depends on usage)
- **Agno AI**: Check pricing
- **Total**: ~$85-135/month

## Migration to ECS (Future)

When ready to scale:
1. Create ECS cluster
2. Convert docker-compose to task definitions
3. Set up RDS and ElastiCache
4. Configure ALB and auto-scaling
5. Migrate data and DNS

This plan focuses on getting you up and running quickly as a solo developer with a clear path to scale later.