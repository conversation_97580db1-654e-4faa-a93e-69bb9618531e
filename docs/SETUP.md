# WhatsApp CRM Agent - Setup Guide

Complete setup guide for the WhatsApp CRM Agent using Agno AI framework.

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Access to your existing cloud PostgreSQL database
- Access to your existing cloud Redis instance
- Chrome browser (for WhatsApp Web automation)

### 1. Clone and Setup

```bash
git clone <your-repo-url>
cd whatsapp-agent
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your API keys and configuration
nano .env
```

**Required Environment Variables:**

```bash
# AI APIs
OPENAI_API_KEY=your_openai_api_key_here
AGNO_API_KEY=your_agno_api_key_here

# Ticketing Systems
FRESHDESK_API_KEY=your_freshdesk_api_key_here
FRESHDESK_DOMAIN=your_domain.freshdesk.com
JIRA_API_TOKEN=your_jira_api_token_here
JIRA_SERVER_URL=https://your-domain.atlassian.net
JIRA_EMAIL=<EMAIL>

# Application
SECRET_KEY=your_secret_key_here_generate_a_strong_one
DEBUG=false
LOG_LEVEL=INFO
```

### 3. Installation and Setup

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run the application
python src/main.py
```

# View logs
tail -f logs/whatsapp-agent.log
```

## 🏗️ Architecture Overview

### Core Components

1. **Conversation Agent** - Primary WhatsApp message handler
2. **Ticketing Agent** - Support ticket lifecycle management  
3. **Knowledge Agent** - Solution recommendations from knowledge base
4. **CRM Agent** - Client relationship management
5. **Analytics Agent** - Reporting and insights generation

### Integration Layer

- **WhatsApp Web** - Selenium-based automation (MVP)
- **Freshdesk** - Customer support ticketing
- **Jira** - Technical issue tracking
- **OpenAI** - AI-powered message processing

### Data Layer

- **PostgreSQL** - Primary database for conversations, tickets, clients
- **Redis** - Caching and session management

## 📁 Project Structure

```
whatsapp-agent/
├── requirements.txt          # Python dependencies
├── .env.example             # Environment template
├── README.md                # Project overview
├── SETUP.md                 # This setup guide
├── agno_ai_architecture.md  # Agent architecture details
├── solo_dev_plan.md         # Development plan
│
├── src/                     # Application source code
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration management
│   ├── database.py          # Database connection and session management
│   ├── monitoring.py        # Logging and monitoring setup
│   │
│   ├── agents/              # Agno AI agents
│   │   ├── conversation.py  # Primary WhatsApp handler
│   │   ├── ticketing.py     # Ticket management
│   │   ├── knowledge.py     # Knowledge base and solutions
│   │   ├── crm.py          # Client relationship management
│   │   └── analytics.py    # Reporting and insights
│   │
│   ├── tools/               # Agent tools
│   │   ├── whatsapp.py      # WhatsApp operations
│   │   └── ticketing.py     # Ticketing system APIs
│   │
│   └── integrations/        # External service integrations
│       ├── whatsapp_web.py  # WhatsApp Web automation
│       ├── freshdesk.py     # Freshdesk API client
│       ├── jira.py          # Jira API client
│       └── openai_client.py # OpenAI API wrapper
│
├── scripts/                 # Deployment and utility scripts
│   ├── setup_ec2.sh         # EC2 instance setup
│   ├── deploy.sh            # Deployment script
│   ├── backup.sh            # Backup and restore
│   └── init.sql             # Database initialization
│
└── deployment/              # Deployment configurations
    └── nginx.conf           # Nginx reverse proxy config
```

## 🔧 Configuration Details

### WhatsApp Setup

1. **Initial Setup:**
   - Run the application with `WHATSAPP_HEADLESS=false`
   - Navigate to WhatsApp Web interface
   - Scan QR code with your phone
   - Session will be saved for future use

2. **Group Management:**
   - Add groups to monitor in the database
   - Configure auto-response settings per group
   - Set escalation rules

### API Keys Setup

1. **OpenAI API:**
   - Get API key from https://platform.openai.com/
   - Add to `OPENAI_API_KEY` in .env

2. **Agno AI:**
   - Get API key from Agno AI platform
   - Add to `AGNO_API_KEY` in .env

3. **Freshdesk:**
   - Go to Admin → API → Your API Key
   - Add to `FRESHDESK_API_KEY` and `FRESHDESK_DOMAIN`

4. **Jira:**
   - Create API token in Atlassian Account Settings
   - Add to `JIRA_API_TOKEN`, `JIRA_SERVER_URL`, `JIRA_EMAIL`

## 🚀 Deployment

### EC2 Deployment

1. **Launch EC2 Instance:**
   - Ubuntu 22.04 LTS
   - t3.medium (2 vCPU, 4GB RAM)
   - 20GB SSD storage
   - Security groups: SSH (22), HTTP (80), HTTPS (443), Custom (8000)

2. **Run Setup Script:**
   ```bash
   # Copy setup script to EC2
   scp scripts/setup_ec2.sh ubuntu@your-ec2-ip:~/
   
   # SSH to EC2 and run setup
   ssh ubuntu@your-ec2-ip
   chmod +x setup_ec2.sh
   ./setup_ec2.sh
   ```

3. **Deploy Application:**
   ```bash
   # Clone repository
   cd /opt/whatsapp-agent
   git clone <your-repo-url> .
   
   # Configure environment
   cp .env.example .env
   nano .env  # Add your API keys
   
   # Deploy
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh
   ```

### SSL Setup (Optional)

```bash
# Install SSL certificate with Let's Encrypt
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring and Maintenance

### Health Checks

- **Application Health:** `GET /health`
- **System Status:** `GET /status`
- **Logs:** `tail -f logs/whatsapp-agent.log`

### Backup and Restore

```bash
# Create backup
./scripts/backup.sh backup

# List backups
./scripts/backup.sh list

# Restore from backup
./scripts/backup.sh restore YYYYMMDD_HHMMSS
```

### Log Management

Logs are automatically rotated and stored in:
- `logs/whatsapp-agent.log` - Main application log
- `logs/errors.log` - Error-only log
- `logs/agents_*.log` - Individual agent logs
- `logs/integrations_*.log` - Integration logs

### Performance Monitoring

- **Metrics Endpoint:** `GET /analytics/kpis`
- **Analytics Report:** `GET /analytics/report`
- **System Metrics:** Built-in performance monitoring

## 🔍 Troubleshooting

### Common Issues

1. **WhatsApp Connection Failed:**
   - Check Chrome installation
   - Verify session directory permissions
   - Try with `WHATSAPP_HEADLESS=false` for debugging

2. **Cloud Service Connection Error:**
   - Verify your cloud PostgreSQL is accessible
   - Check your cloud Redis is accessible
   - Ensure network connectivity to cloud services

3. **API Integration Failures:**
   - Verify API keys are correct
   - Check network connectivity
   - Review integration logs

4. **Agent Execution Errors:**
   - Check Agno AI API key
   - Verify OpenAI API limits
   - Review agent-specific logs

### Debug Mode

```bash
# Enable debug mode
export DEBUG=true
export LOG_LEVEL=DEBUG

# Run with verbose logging
python src/main.py
```

### Log Analysis

```bash
# View recent errors
tail -f logs/errors.log

# Search for specific issues
grep -i "error" logs/whatsapp-agent.log

# Monitor agent activity
tail -f logs/agents_conversation.log
```

## 📈 Scaling and Optimization

### Performance Tuning

1. **Database Optimization:**
   - Add indexes for frequently queried fields
   - Configure connection pooling
   - Regular VACUUM and ANALYZE

2. **Redis Configuration:**
   - Adjust memory limits
   - Configure persistence
   - Monitor memory usage

3. **Application Scaling:**
   - Use multiple worker processes
   - Implement load balancing
   - Consider horizontal scaling

### Migration to ECS (Future)

When ready to scale:

1. Deploy multiple application instances
2. Use load balancers for distribution
3. Scale your existing cloud database services as needed
4. Configure Application Load Balancer
5. Implement auto-scaling policies

## 🆘 Support

### Getting Help

1. **Check Logs:** Always start with application logs
2. **Health Checks:** Verify all services are healthy
3. **Documentation:** Review architecture and API docs
4. **Community:** Check Agno AI community forums

### Reporting Issues

When reporting issues, include:
- Error messages from logs
- Steps to reproduce
- Environment details
- Configuration (without sensitive data)

## 🔐 Security Considerations

### Production Security

1. **Environment Variables:**
   - Never commit .env files
   - Use strong, unique passwords
   - Rotate API keys regularly

2. **Network Security:**
   - Configure firewall rules
   - Use HTTPS in production
   - Implement rate limiting

3. **Access Control:**
   - Limit SSH access
   - Use key-based authentication
   - Regular security updates

4. **Data Protection:**
   - Encrypt sensitive data
   - Regular backups
   - Secure backup storage

---

## 🎉 You're Ready!

Your WhatsApp CRM Agent is now set up and ready to handle customer conversations with AI-powered automation. The system will:

- ✅ Process WhatsApp messages automatically
- ✅ Create tickets in Freshdesk/Jira when needed
- ✅ Provide intelligent responses using knowledge base
- ✅ Track customer interactions for CRM
- ✅ Generate analytics and insights

For ongoing maintenance, use the provided scripts and monitoring tools to keep your system running smoothly.