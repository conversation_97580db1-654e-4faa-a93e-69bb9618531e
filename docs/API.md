# WhatsApp CRM Agent - API Documentation

Complete API reference for the WhatsApp CRM Agent.

## 🔗 Base URL

```
http://localhost:8000  # Local development
https://your-domain.com  # Production
```

## 🔐 Authentication

Currently, the API uses basic authentication. In production, implement proper JWT or API key authentication.

## 📋 Endpoints

### Health Check

#### GET /health

Check application health status.

**Response:**
```json
{
  "status": "healthy",
  "service": "WhatsApp CRM Agent",
  "version": "1.0.0"
}
```

---

### WhatsApp Operations

#### POST /webhook/whatsapp

Receive WhatsApp messages via webhook.

**Request Body:**
```json
{
  "group_id": "string",
  "sender_id": "string", 
  "sender_name": "string",
  "content": "string",
  "message_type": "text"
}
```

**Response:**
```json
{
  "status": "received",
  "message_id": "uuid"
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/webhook/whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "customer_support_group",
    "sender_id": "**********",
    "sender_name": "John Doe",
    "content": "I need help with my account",
    "message_type": "text"
  }'
```

#### POST /send-message

Send a message to a WhatsApp group.

**Request Body:**
```json
{
  "group_id": "string",
  "message": "string"
}
```

**Response:**
```json
{
  "status": "sent",
  "result": "Message sent successfully to group customer_support_group"
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/send-message" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "customer_support_group",
    "message": "Thank you for contacting us. How can we help you today?"
  }'
```

---

### Analytics

#### GET /analytics/report

Get comprehensive analytics report.

**Query Parameters:**
- `days` (optional): Number of days to analyze (default: 30)
- `group_id` (optional): Specific group ID to analyze

**Response:**
```json
{
  "report": {
    "period": {
      "days": 30,
      "start_date": "2024-01-01T00:00:00",
      "end_date": "2024-01-31T23:59:59"
    },
    "scope": "overall",
    "messages": {
      "total": 1250,
      "from_users": 800,
      "from_bot": 450,
      "bot_response_rate": "56.3%"
    },
    "conversations": {
      "total": 150,
      "active": 25,
      "escalated": 12,
      "escalation_rate": "8.0%"
    },
    "tickets": {
      "total": 45,
      "open": 8,
      "resolved": 37,
      "resolution_rate": "82.2%",
      "by_system": {
        "freshdesk": 30,
        "jira": 15
      }
    },
    "clients": {
      "total": 85,
      "active": 78,
      "vip": 12
    }
  }
}
```

**Example:**
```bash
curl "http://localhost:8000/analytics/report?days=7&group_id=customer_support_group"
```

#### GET /analytics/kpis

Get key performance indicators.

**Response:**
```json
{
  "kpis": {
    "success": true,
    "analytics": {
      "period_days": 7,
      "total_messages": 350,
      "user_messages": 200,
      "bot_messages": 150,
      "response_rate": "75.0%"
    }
  }
}
```

**Example:**
```bash
curl "http://localhost:8000/analytics/kpis"
```

---

### Client Management

#### GET /clients/{client_id}

Get client information by ID.

**Path Parameters:**
- `client_id`: UUID of the client

**Response:**
```json
{
  "id": "uuid",
  "name": "John Doe",
  "phone": "+**********",
  "email": "<EMAIL>",
  "company": "Acme Corp",
  "is_vip": false,
  "created_at": "2024-01-15T10:30:00"
}
```

**Example:**
```bash
curl "http://localhost:8000/clients/123e4567-e89b-12d3-a456-************"
```

---

### Conversation Management

#### GET /conversations/{group_id}

Get conversation history for a WhatsApp group.

**Path Parameters:**
- `group_id`: WhatsApp group identifier

**Query Parameters:**
- `limit` (optional): Maximum number of messages to return (default: 50)

**Response:**
```json
{
  "conversation_id": "uuid",
  "group_id": "customer_support_group",
  "messages": [
    {
      "id": "uuid",
      "sender_id": "**********",
      "sender_name": "John Doe",
      "content": "I need help with my account",
      "message_type": "text",
      "is_from_bot": false,
      "created_at": "2024-01-15T10:30:00"
    },
    {
      "id": "uuid",
      "sender_id": "bot",
      "sender_name": "WhatsApp Agent",
      "content": "I'd be happy to help you with your account. What specific issue are you experiencing?",
      "message_type": "text",
      "is_from_bot": true,
      "created_at": "2024-01-15T10:30:15"
    }
  ],
  "total_messages": 2
}
```

**Example:**
```bash
curl "http://localhost:8000/conversations/customer_support_group?limit=20"
```

---

## 🛠️ Agent Tools API

The following endpoints are used internally by the AI agents but can also be called directly for testing or integration purposes.

### WhatsApp Tools

#### POST /tools/whatsapp/send-message

Internal tool for sending WhatsApp messages.

#### GET /tools/whatsapp/conversation-history

Internal tool for getting conversation history.

#### POST /tools/whatsapp/escalate

Internal tool for escalating conversations to human agents.

### Ticketing Tools

#### POST /tools/ticketing/create-freshdesk-ticket

Create a ticket in Freshdesk.

**Request Body:**
```json
{
  "group_id": "string",
  "title": "string",
  "description": "string",
  "priority": "normal",
  "category": "General"
}
```

#### POST /tools/ticketing/create-jira-ticket

Create a ticket in Jira.

**Request Body:**
```json
{
  "group_id": "string",
  "title": "string", 
  "description": "string",
  "priority": "normal",
  "issue_type": "Task"
}
```

#### PUT /tools/ticketing/update-ticket-status

Update ticket status.

**Request Body:**
```json
{
  "ticket_id": "string",
  "status": "string",
  "system": "freshdesk"
}
```

### Database Tools

#### POST /tools/database/save-message

Save a message to the database.

#### GET /tools/database/client-info

Get client information.

#### PUT /tools/database/update-client-profile

Update client profile information.

---

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request - Invalid input |
| 401  | Unauthorized |
| 404  | Not Found |
| 500  | Internal Server Error |

## 🔄 Webhooks

### WhatsApp Webhook

Configure your WhatsApp integration to send messages to:
```
POST /webhook/whatsapp
```

**Expected Payload:**
```json
{
  "group_id": "group_identifier",
  "sender_id": "sender_whatsapp_id",
  "sender_name": "Sender Display Name",
  "content": "Message content",
  "message_type": "text",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Ticketing System Webhooks

Configure your ticketing systems to send updates to:
```
POST /webhook/freshdesk
POST /webhook/jira
```

---

## 🧪 Testing

### Health Check Test

```bash
curl -f http://localhost:8000/health || echo "Service is down"
```

### Message Processing Test

```bash
# Send a test message
curl -X POST "http://localhost:8000/webhook/whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "test_group",
    "sender_id": "test_user",
    "sender_name": "Test User",
    "content": "Hello, I need help with billing",
    "message_type": "text"
  }'

# Check if response was generated
curl "http://localhost:8000/conversations/test_group?limit=5"
```

### Analytics Test

```bash
# Get current KPIs
curl "http://localhost:8000/analytics/kpis"

# Get weekly report
curl "http://localhost:8000/analytics/report?days=7"
```

---

## 🔧 Configuration

### Environment Variables

The API behavior can be configured using environment variables:

```bash
# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# Rate Limiting (if implemented)
RATE_LIMIT_PER_MINUTE=60
```

### Custom Headers

Include these headers in your requests:

```bash
Content-Type: application/json
Accept: application/json
User-Agent: YourApp/1.0
```

---

## 📝 Examples

### Complete Workflow Example

```bash
#!/bin/bash

# 1. Check service health
echo "Checking service health..."
curl -f http://localhost:8000/health

# 2. Send a customer message
echo "Sending customer message..."
MESSAGE_RESPONSE=$(curl -s -X POST "http://localhost:8000/webhook/whatsapp" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": "support_group",
    "sender_id": "customer123",
    "sender_name": "Jane Smith",
    "content": "My payment failed, please help",
    "message_type": "text"
  }')

echo "Message response: $MESSAGE_RESPONSE"

# 3. Wait for processing
sleep 5

# 4. Check conversation history
echo "Checking conversation history..."
curl -s "http://localhost:8000/conversations/support_group?limit=10" | jq .

# 5. Get analytics
echo "Getting analytics..."
curl -s "http://localhost:8000/analytics/kpis" | jq .
```

### Python SDK Example

```python
import requests
import json

class WhatsAppCRMClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def send_message(self, group_id, message):
        """Send a message to WhatsApp group"""
        response = self.session.post(
            f"{self.base_url}/send-message",
            json={"group_id": group_id, "message": message}
        )
        return response.json()
    
    def get_conversation_history(self, group_id, limit=50):
        """Get conversation history"""
        response = self.session.get(
            f"{self.base_url}/conversations/{group_id}",
            params={"limit": limit}
        )
        return response.json()
    
    def get_analytics_report(self, days=30, group_id=None):
        """Get analytics report"""
        params = {"days": days}
        if group_id:
            params["group_id"] = group_id
        
        response = self.session.get(
            f"{self.base_url}/analytics/report",
            params=params
        )
        return response.json()

# Usage
client = WhatsAppCRMClient()

# Send a message
result = client.send_message("support_group", "Hello! How can I help you today?")
print(f"Message sent: {result}")

# Get conversation history
history = client.get_conversation_history("support_group", limit=10)
print(f"Found {len(history['messages'])} messages")

# Get analytics
analytics = client.get_analytics_report(days=7)
print(f"Analytics: {analytics}")
```

---

## 🚨 Error Handling

### Common Error Responses

```json
{
  "detail": "Error message description",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Codes

- `WHATSAPP_CONNECTION_FAILED` - WhatsApp Web connection issues
- `DATABASE_ERROR` - Database operation failed
- `API_INTEGRATION_ERROR` - External API call failed
- `AGENT_EXECUTION_ERROR` - AI agent processing failed
- `VALIDATION_ERROR` - Input validation failed

### Retry Logic

Implement exponential backoff for failed requests:

```python
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_session_with_retries():
    session = requests.Session()
    
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    return session
```

---

This API documentation provides comprehensive coverage of all available endpoints and their usage. For additional support or feature requests, please refer to the main documentation or contact the development team.