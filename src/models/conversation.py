"""
Conversation and Group models
"""
from sqlalchemy import Column, String, <PERSON><PERSON><PERSON>, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .base import BaseModel


class Group(BaseModel):
    """WhatsApp Group model"""
    __tablename__ = "groups"
    
    whatsapp_group_id = Column(String(255), unique=True, nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    auto_respond = Column(Boolean, default=True, nullable=False)

    conversations = relationship("Conversation", back_populates="group", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Group(name='{self.name}', whatsapp_id='{self.whatsapp_group_id}')>"


class Conversation(BaseModel):
    """Conversation model"""
    __tablename__ = "conversations"
    
    client_id = Column(UUID(as_uuid=True), ForeignKey("clients.id"), nullable=False)
    group_id = Column(UUID(as_uuid=True), ForeignKey("groups.id"), nullable=False)
    status = Column(String(50), default="active", nullable=False)
    escalated_to_human = Column(Boolean, default=False, nullable=False)
    summary = Column(Text, nullable=True)
    agent_notes = Column(Text, nullable=True)

    client = relationship("Client", back_populates="conversations")
    group = relationship("Group", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    tickets = relationship("Ticket", back_populates="conversation", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Conversation(client_id='{self.client_id}', status='{self.status}')>"
