"""
Support ticket model
"""
from sqlalchemy import Column, String, ForeignKey, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .base import BaseModel


class Ticket(BaseModel):
    """Support ticket model"""
    __tablename__ = "tickets"
    
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String(100), nullable=True)
    priority = Column(String(50), default="normal", nullable=False)
    status = Column(String(50), default="open", nullable=False)
    system = Column(String(50), nullable=False)
    external_ticket_id = Column(String(255), nullable=True)
    external_url = Column(String(500), nullable=True)
    assigned_to = Column(String(255), nullable=True)
    resolution = Column(Text, nullable=True)
    resolution_time_minutes = Column(Integer, nullable=True)

    conversation = relationship("Conversation", back_populates="tickets")
    
    def __repr__(self):
        return f"<Ticket(title='{self.title}', system='{self.system}', status='{self.status}')>"
