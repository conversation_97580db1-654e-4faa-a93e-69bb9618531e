"""
Client/Customer model
"""
from sqlalchemy import Column, String, Boolean, Text
from sqlalchemy.orm import relationship
from .base import BaseModel


class Client(BaseModel):
    """Client/Customer model"""
    __tablename__ = "clients"
    
    name = Column(String(255), nullable=False)
    phone = Column(String(20), unique=True, nullable=False)
    email = Column(String(255), unique=True, nullable=True)
    company = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_vip = Column(Boolean, default=False, nullable=False)
    notes = Column(Text, nullable=True)

    conversations = relationship("Conversation", back_populates="client", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Client(name='{self.name}', phone='{self.phone}')>"
