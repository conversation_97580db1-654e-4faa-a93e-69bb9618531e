"""
Message model
"""
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .base import BaseModel


class Message(BaseModel):
    """Message model"""
    __tablename__ = "messages"
    
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String(50), default="text", nullable=False)
    sender_id = Column(String(255), nullable=False)
    sender_name = Column(String(255), nullable=True)
    is_from_bot = Column(Boolean, default=False, nullable=False)
    whatsapp_message_id = Column(String(255), unique=True, nullable=True)
    reply_to_message_id = Column(UUID(as_uuid=True), ForeignKey("messages.id"), nullable=True)
    processed = Column(Bo<PERSON>an, default=False, nullable=False)
    processing_error = Column(Text, nullable=True)

    conversation = relationship("Conversation", back_populates="messages")
    reply_to = relationship("Message", remote_side="Message.id")
    
    def __repr__(self):
        return f"<Message(sender='{self.sender_name}', type='{self.message_type}', bot={self.is_from_bot})>"
