"""
Database tools for WhatsApp CRM Agent
"""
from typing import Dict, Any, Optional, List
from agno.tools import tool
import logging
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from ..database import get_db_session
from ..models import Message, Conversation, Client, Group, Ticket

logger = logging.getLogger(__name__)


@tool
def save_message(
    group_id: str,
    sender_id: str,
    content: str,
    sender_name: Optional[str] = None,
    message_type: str = "text",
    is_from_bot: bool = False
) -> Dict[str, Any]:
    """Save a message to the cloud database"""
    try:
        with get_db_session() as db:
            group = db.query(Group).filter(Group.whatsapp_group_id == group_id).first()
            if not group:
                group = Group(
                    whatsapp_group_id=group_id,
                    name=f"Group {group_id}",
                    description="Auto-created group"
                )
                db.add(group)
                db.flush()

            client = db.query(Client).filter(Client.phone == sender_id).first()
            if not client:
                client = Client(
                    name=sender_name or f"User {sender_id}",
                    phone=sender_id,
                    is_active=True
                )
                db.add(client)
                db.flush()
            conversation = db.query(Conversation).filter(
                and_(
                    Conversation.client_id == client.id,
                    Conversation.group_id == group.id,
                    Conversation.status == "active"
                )
            ).first()
            
            if not conversation:
                conversation = Conversation(
                    client_id=client.id,
                    group_id=group.id,
                    status="active"
                )
                db.add(conversation)
                db.flush()

            message = Message(
                conversation_id=conversation.id,
                content=content,
                message_type=message_type,
                sender_id=sender_id,
                sender_name=sender_name,
                is_from_bot=is_from_bot,
                processed=False
            )
            db.add(message)
            db.flush()
            
            return {
                "success": True,
                "message_id": str(message.id),
                "conversation_id": str(conversation.id)
            }
            
    except Exception as e:
        logger.error(f"Error saving message: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool
def get_client_info(client_id: Optional[str] = None, phone: Optional[str] = None, group_id: Optional[str] = None) -> Dict[str, Any]:
    """Get client information from cloud database"""
    try:
        with get_db_session() as db:
            query = db.query(Client)
            
            if client_id:
                client = query.filter(Client.id == client_id).first()
            elif phone:
                client = query.filter(Client.phone == phone).first()
            elif group_id:
                # Find client through group conversation
                client = query.join(Conversation).join(Group).filter(
                    Group.whatsapp_group_id == group_id
                ).first()
            else:
                return {"success": False, "error": "No identifier provided"}
            
            if not client:
                return {"success": False, "error": "Client not found"}
            
            return {
                "success": True,
                "client": {
                    "id": str(client.id),
                    "name": client.name,
                    "phone": client.phone,
                    "email": client.email,
                    "company": client.company,
                    "is_active": client.is_active,
                    "is_vip": client.is_vip,
                    "notes": client.notes,
                    "created_at": client.created_at.isoformat()
                }
            }
            
    except Exception as e:
        logger.error(f"Error getting client info: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool
def get_conversation_analytics(group_id: Optional[str] = None, days: int = 30) -> Dict[str, Any]:
    """Get conversation analytics from cloud database"""
    try:
        with get_db_session() as db:
            since_date = datetime.now() - timedelta(days=days)

            # Base queries
            message_query = db.query(Message).filter(Message.created_at >= since_date)
            conversation_query = db.query(Conversation).filter(Conversation.created_at >= since_date)

            if group_id:
                message_query = message_query.join(Conversation).join(Group).filter(
                    Group.whatsapp_group_id == group_id
                )
                conversation_query = conversation_query.join(Group).filter(
                    Group.whatsapp_group_id == group_id
                )

            # Message analytics
            total_messages = message_query.count()
            bot_messages = message_query.filter(Message.is_from_bot == True).count()
            user_messages = total_messages - bot_messages

            # Conversation analytics
            total_conversations = conversation_query.count()
            active_conversations = conversation_query.filter(Conversation.status == "active").count()
            escalated_conversations = conversation_query.filter(Conversation.escalated_to_human == True).count()

            return {
                "success": True,
                "analytics": {
                    "total_messages": total_messages,
                    "user_messages": user_messages,
                    "bot_messages": bot_messages,
                    "total_conversations": total_conversations,
                    "active_conversations": active_conversations,
                    "escalated_conversations": escalated_conversations,
                    "period_days": days
                }
            }

    except Exception as e:
        logger.error(f"Error getting conversation analytics: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool
def update_client_profile(client_id: str, **updates) -> Dict[str, Any]:
    """Update client profile in cloud database"""
    try:
        with get_db_session() as db:
            client = db.query(Client).filter(Client.id == client_id).first()
            if not client:
                return {"success": False, "error": "Client not found"}

            # Update allowed fields
            allowed_fields = ["name", "email", "company", "is_vip", "notes"]
            for field, value in updates.items():
                if field in allowed_fields and hasattr(client, field):
                    setattr(client, field, value)

            return {
                "success": True,
                "client_id": str(client.id),
                "updated_fields": list(updates.keys())
            }

    except Exception as e:
        logger.error(f"Error updating client profile: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }


@tool
def track_interaction(client_id: str, interaction_type: str, details: str) -> Dict[str, Any]:
    """Track client interaction"""
    try:
        with get_db_session() as db:
            client = db.query(Client).filter(Client.id == client_id).first()
            if not client:
                return {"success": False, "error": "Client not found"}

            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            interaction_note = f"[{timestamp}] {interaction_type}: {details}"

            if client.notes:
                client.notes += f"\n{interaction_note}"
            else:
                client.notes = interaction_note

            return {
                "success": True,
                "client_id": str(client.id),
                "interaction_logged": True
            }

    except Exception as e:
        logger.error(f"Error tracking interaction: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
