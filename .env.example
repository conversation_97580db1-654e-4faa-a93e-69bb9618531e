# Database Configuration
# Cloud PostgreSQL - Replace with your cloud database URL
DATABASE_URL=***************************************************/database_name
# Redis - Cloud Redis instance
REDIS_URL=redis://:NseTdSpPQkmPb%2BJ6xp%26KM*HM@************:6379

# AI API Keys
OPENAI_API_KEY=your_openai_api_key_here
AGNO_API_KEY=your_agno_api_key_here

# Ticketing System APIs
FRESHDESK_API_KEY=your_freshdesk_api_key_here
FRESHDESK_DOMAIN=your_domain.freshdesk.com
JIRA_API_TOKEN=your_jira_api_token_here
JIRA_SERVER_URL=https://your-domain.atlassian.net
JIRA_EMAIL=<EMAIL>

# WhatsApp Configuration
WHATSAPP_HEADLESS=true
WHATSAPP_SESSION_PATH=./whatsapp_session

# Application Settings
SECRET_KEY=your_secret_key_here_generate_a_strong_one
DEBUG=false
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=8001

# Security
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]